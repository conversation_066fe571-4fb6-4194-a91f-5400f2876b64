package com.sprintbot.medicaladmin.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Entité représentant les données de santé d'un joueur
 * Gère le suivi médical, bilans et historique des blessures
 */
@Entity
@Table(name = "donnees_sante")
@EntityListeners(AuditingEntityListener.class)
public class DonneesSante {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "L'ID du joueur est obligatoire")
    @Column(name = "joueur_id", nullable = false)
    private Long joueurId;

    @NotNull(message = "L'ID du staff médical est obligatoire")
    @Column(name = "staff_medical_id", nullable = false)
    private Long staffMedicalId;

    @NotBlank(message = "Le type d'examen est obligatoire")
    @Size(max = 100, message = "Le type d'examen ne peut pas dépasser 100 caractères")
    @Column(name = "type_examen", nullable = false, length = 100)
    private String typeExamen; // BILAN_GENERAL, BLESSURE, SUIVI, PREVENTION

    @Column(name = "bilan", columnDefinition = "TEXT")
    private String bilan;

    @Column(name = "diagnostic", columnDefinition = "TEXT")
    private String diagnostic;

    @Column(name = "traitement", columnDefinition = "TEXT")
    private String traitement;

    @Column(name = "recommandations", columnDefinition = "TEXT")
    private String recommandations;

    @Column(name = "blessures", columnDefinition = "TEXT")
    private String blessures;

    @Size(max = 50, message = "La gravité ne peut pas dépasser 50 caractères")
    @Column(name = "gravite", length = 50)
    private String gravite; // LEGERE, MODEREE, GRAVE, CRITIQUE

    @NotNull(message = "La date d'examen est obligatoire")
    @Column(name = "date_examen", nullable = false)
    private LocalDate dateExamen;

    @Column(name = "date_guerison_prevue")
    private LocalDate dateGuerisonPrevue;

    @Size(max = 50, message = "Le statut ne peut pas dépasser 50 caractères")
    @Column(name = "statut", length = 50)
    private String statut = "ACTIF"; // ACTIF, GUERI, EN_TRAITEMENT, SUIVI

    @Column(name = "notes_privees", columnDefinition = "TEXT")
    private String notesPrivees;

    @Column(name = "visible_joueur")
    private Boolean visibleJoueur = true;

    @Column(name = "poids")
    private Double poids;

    @Column(name = "taille")
    private Double taille;

    @Column(name = "tension_arterielle", length = 20)
    private String tensionArterielle;

    @Column(name = "frequence_cardiaque")
    private Integer frequenceCardiaque;

    @CreatedDate
    @Column(name = "date_creation", nullable = false, updatable = false)
    private LocalDateTime dateCreation;

    @LastModifiedDate
    @Column(name = "date_modification")
    private LocalDateTime dateModification;

    // Relations
    @OneToMany(mappedBy = "donneesSante", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<RendezVousMedical> rendezVous = new ArrayList<>();

    // Constructeurs
    public DonneesSante() {
        this.dateExamen = LocalDate.now();
        this.statut = "ACTIF";
        this.visibleJoueur = true;
    }

    public DonneesSante(Long joueurId, Long staffMedicalId, String typeExamen, String bilan) {
        this();
        this.joueurId = joueurId;
        this.staffMedicalId = staffMedicalId;
        this.typeExamen = typeExamen;
        this.bilan = bilan;
    }

    // Méthodes métier
    public void ajouterBlessure(String nouvelleBlessure) {
        if (this.blessures == null || this.blessures.isEmpty()) {
            this.blessures = nouvelleBlessure;
        } else {
            this.blessures += "; " + nouvelleBlessure;
        }
    }

    public void marquerCommeGueri() {
        this.statut = "GUERI";
        this.dateGuerisonPrevue = LocalDate.now();
    }

    public void commencerTraitement() {
        this.statut = "EN_TRAITEMENT";
    }

    public void mettreEnSuivi() {
        this.statut = "SUIVI";
    }

    public boolean estGrave() {
        return "GRAVE".equals(this.gravite) || "CRITIQUE".equals(this.gravite);
    }

    public boolean necessiteSuivi() {
        return "EN_TRAITEMENT".equals(this.statut) || "SUIVI".equals(this.statut);
    }

    public long getJoursDepuisExamen() {
        return this.dateExamen.until(LocalDate.now()).getDays();
    }

    public boolean estRecent() {
        return getJoursDepuisExamen() <= 30;
    }

    // Getters et Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getJoueurId() { return joueurId; }
    public void setJoueurId(Long joueurId) { this.joueurId = joueurId; }

    public Long getStaffMedicalId() { return staffMedicalId; }
    public void setStaffMedicalId(Long staffMedicalId) { this.staffMedicalId = staffMedicalId; }

    public String getTypeExamen() { return typeExamen; }
    public void setTypeExamen(String typeExamen) { this.typeExamen = typeExamen; }

    public String getBilan() { return bilan; }
    public void setBilan(String bilan) { this.bilan = bilan; }

    public String getDiagnostic() { return diagnostic; }
    public void setDiagnostic(String diagnostic) { this.diagnostic = diagnostic; }

    public String getTraitement() { return traitement; }
    public void setTraitement(String traitement) { this.traitement = traitement; }

    public String getRecommandations() { return recommandations; }
    public void setRecommandations(String recommandations) { this.recommandations = recommandations; }

    public String getBlessures() { return blessures; }
    public void setBlessures(String blessures) { this.blessures = blessures; }

    public String getGravite() { return gravite; }
    public void setGravite(String gravite) { this.gravite = gravite; }

    public LocalDate getDateExamen() { return dateExamen; }
    public void setDateExamen(LocalDate dateExamen) { this.dateExamen = dateExamen; }

    public LocalDate getDateGuerisonPrevue() { return dateGuerisonPrevue; }
    public void setDateGuerisonPrevue(LocalDate dateGuerisonPrevue) { this.dateGuerisonPrevue = dateGuerisonPrevue; }

    public String getStatut() { return statut; }
    public void setStatut(String statut) { this.statut = statut; }

    public String getNotesPrivees() { return notesPrivees; }
    public void setNotesPrivees(String notesPrivees) { this.notesPrivees = notesPrivees; }

    public Boolean getVisibleJoueur() { return visibleJoueur; }
    public void setVisibleJoueur(Boolean visibleJoueur) { this.visibleJoueur = visibleJoueur; }

    public Double getPoids() { return poids; }
    public void setPoids(Double poids) { this.poids = poids; }

    public Double getTaille() { return taille; }
    public void setTaille(Double taille) { this.taille = taille; }

    public String getTensionArterielle() { return tensionArterielle; }
    public void setTensionArterielle(String tensionArterielle) { this.tensionArterielle = tensionArterielle; }

    public Integer getFrequenceCardiaque() { return frequenceCardiaque; }
    public void setFrequenceCardiaque(Integer frequenceCardiaque) { this.frequenceCardiaque = frequenceCardiaque; }

    public LocalDateTime getDateCreation() { return dateCreation; }
    public void setDateCreation(LocalDateTime dateCreation) { this.dateCreation = dateCreation; }

    public LocalDateTime getDateModification() { return dateModification; }
    public void setDateModification(LocalDateTime dateModification) { this.dateModification = dateModification; }

    public List<RendezVousMedical> getRendezVous() { return rendezVous; }
    public void setRendezVous(List<RendezVousMedical> rendezVous) { this.rendezVous = rendezVous; }

    // Méthodes utilitaires pour les relations
    public void ajouterRendezVous(RendezVousMedical rendezVous) {
        this.rendezVous.add(rendezVous);
        rendezVous.setDonneesSante(this);
    }

    public void retirerRendezVous(RendezVousMedical rendezVous) {
        this.rendezVous.remove(rendezVous);
        rendezVous.setDonneesSante(null);
    }

    @Override
    public String toString() {
        return "DonneesSante{" +
                "id=" + id +
                ", joueurId=" + joueurId +
                ", typeExamen='" + typeExamen + '\'' +
                ", dateExamen=" + dateExamen +
                ", statut='" + statut + '\'' +
                '}';
    }
}
