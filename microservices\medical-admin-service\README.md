# 🏥 Medical-Admin-Service

## Vue d'ensemble

Le **medical-admin-service** est un microservice dédié à la gestion médicale et administrative de la plateforme SprintBot. Il gère les données de santé des joueurs, les rendez-vous médicaux et les demandes administratives internes.

## 🎯 Fonctionnalités

### 🩺 **Gestion Médicale**
- **Données de santé** : Suivi médical des joueurs
- **Bilans médicaux** : Évaluations et diagnostics
- **Gestion des blessures** : Historique et suivi
- **Dossiers médicaux** : Centralisation des informations

### 📅 **Rendez-vous Médicaux**
- **Planification** : Création et gestion des RDV
- **Calendrier médical** : Vue d'ensemble des consultations
- **Notifications** : Rappels automatiques
- **Suivi des consultations** : Historique et résultats

### 📋 **Administration Interne**
- **Demandes administratives** : Workflow de validation
- **Gestion des statuts** : EN_ATTENTE, VALIDEE, REJETEE
- **Traçabilité** : Historique des demandes
- **Notifications** : Alertes pour les responsables

## 🏗️ Architecture

### Structure du Projet
```
medical-admin-service/
├── backend/                    # Application Spring Boot
│   ├── src/main/java/
│   │   └── com/sprintbot/medicaladmin/
│   │       ├── config/         # Configuration Spring
│   │       ├── controller/     # Contrôleurs REST
│   │       ├── dto/           # Data Transfer Objects
│   │       ├── entity/        # Entités JPA
│   │       ├── repository/    # Repositories Spring Data
│   │       └── service/       # Services métier
│   ├── src/main/resources/
│   │   ├── application.yml    # Configuration application
│   │   └── db/migration/      # Scripts de migration
│   └── Dockerfile
├── frontend/                   # Application Angular
│   ├── src/app/
│   │   ├── components/        # Composants Angular
│   │   ├── services/          # Services Angular
│   │   ├── models/           # Modèles TypeScript
│   │   └── guards/           # Guards de navigation
│   └── Dockerfile
├── database/                   # Configuration base de données
│   └── init.sql              # Script d'initialisation
└── docker-compose.yml        # Orchestration Docker
```

## 🗄️ Modèle de Données

### Entités Principales

#### **DonneesSante**
- Suivi médical des joueurs
- Bilans et diagnostics
- Historique des blessures
- Relations avec joueurs et staff médical

#### **RendezVousMedical**
- Planification des consultations
- Gestion des créneaux
- Statuts : EN_ATTENTE, CONFIRME, TERMINE, ANNULE
- Notifications automatiques

#### **DemandeAdministrative**
- Workflow de validation
- Types multiples de demandes
- Traçabilité complète
- Gestion des approbations

## 🔧 Technologies

### Backend
- **Spring Boot 3.x**
- **Spring Security** avec JWT
- **Spring Data JPA**
- **PostgreSQL**
- **Bean Validation**
- **Maven**

### Frontend
- **Angular 17**
- **TypeScript**
- **Angular Material**
- **Bootstrap**
- **Chart.js**
- **RxJS**

### Infrastructure
- **Docker & Docker Compose**
- **PostgreSQL** base dédiée
- **Nginx** reverse proxy
- **Health checks** automatiques

## 🚀 Démarrage Rapide

### Prérequis
- Java 17+
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 15+

### Installation

#### 1. Backend
```bash
cd microservices/medical-admin-service/backend
mvn clean install
mvn spring-boot:run
```

#### 2. Frontend
```bash
cd microservices/medical-admin-service/frontend
npm install
npm start
```

#### 3. Docker (Recommandé)
```bash
cd microservices/medical-admin-service
docker-compose up -d
```

## 📡 API Endpoints

### Données de Santé
- `GET /api/donnees-sante` - Liste des données
- `POST /api/donnees-sante` - Créer données
- `GET /api/donnees-sante/{id}` - Détails
- `PUT /api/donnees-sante/{id}` - Modifier
- `DELETE /api/donnees-sante/{id}` - Supprimer

### Rendez-vous Médicaux
- `GET /api/rendez-vous` - Liste des RDV
- `POST /api/rendez-vous` - Planifier RDV
- `GET /api/rendez-vous/{id}` - Détails RDV
- `PUT /api/rendez-vous/{id}` - Modifier RDV
- `PATCH /api/rendez-vous/{id}/confirmer` - Confirmer
- `PATCH /api/rendez-vous/{id}/annuler` - Annuler

### Demandes Administratives
- `GET /api/demandes` - Liste des demandes
- `POST /api/demandes` - Créer demande
- `GET /api/demandes/{id}` - Détails
- `PATCH /api/demandes/{id}/valider` - Valider
- `PATCH /api/demandes/{id}/rejeter` - Rejeter

## 🔐 Sécurité

### Authentification
- **JWT Tokens** avec access/refresh
- **Rôles** : STAFF_MEDICAL, ADMINISTRATEUR
- **Permissions** granulaires par endpoint

### Autorisation
- **Guards Angular** pour protection des routes
- **Spring Security** pour APIs backend
- **CORS** configuré pour frontend

## 🧪 Tests

### Backend
```bash
mvn test
mvn jacoco:report
```

### Frontend
```bash
npm test
npm run test:coverage
```

## 📊 Monitoring

### Health Checks
- `/actuator/health` - Santé du service
- `/actuator/metrics` - Métriques
- `/actuator/info` - Informations

### Logs
- **SLF4J + Logback**
- **Niveaux** : DEBUG, INFO, WARN, ERROR
- **Rotation** automatique

## 🚀 Déploiement

### Environnements
- **Développement** : `docker-compose.dev.yml`
- **Staging** : `docker-compose.staging.yml`
- **Production** : `docker-compose.prod.yml`

### CI/CD
- **Jenkins Pipeline** automatique
- **Tests** unitaires et intégration
- **Build** Docker multi-stage
- **Déploiement** automatisé

## 📈 Roadmap

### Phase 1 (Actuelle)
- ✅ Entités et repositories
- ✅ Services métier
- ✅ Contrôleurs REST
- ✅ Frontend Angular

### Phase 2
- 🔄 Notifications en temps réel
- 🔄 Rapports médicaux avancés
- 🔄 Intégration calendrier externe
- 🔄 Analytics prédictives

### Phase 3
- 📋 IA pour diagnostic
- 📋 Télémédecine
- 📋 Intégration IoT
- 📋 Mobile app

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature
3. Commit les changements
4. Push vers la branche
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

---

**🏥 Medical-Admin-Service - Gestion médicale et administrative pour SprintBot**
