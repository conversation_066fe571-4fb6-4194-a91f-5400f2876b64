package com.sprintbot.medicaladmin.repository;

import com.sprintbot.medicaladmin.entity.RendezVousMedical;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository pour la gestion des rendez-vous médicaux
 * Fournit les opérations CRUD et requêtes personnalisées
 */
@Repository
public interface RendezVousMedicalRepository extends JpaRepository<RendezVousMedical, Long> {

    // Recherches par joueur
    List<RendezVousMedical> findByJoueurIdOrderByDateRendezVousDescHeureRendezVousDesc(Long joueurId);
    
    Page<RendezVousMedical> findByJoueurIdOrderByDateRendezVousDescHeureRendezVousDesc(Long joueurId, Pageable pageable);
    
    Optional<RendezVousMedical> findFirstByJoueurIdAndStatutInOrderByDateRendezVousAscHeureRendezVousAsc(
            Long joueurId, List<String> statuts);

    // Recherches par staff médical
    List<RendezVousMedical> findByStaffMedicalIdOrderByDateRendezVousAscHeureRendezVousAsc(Long staffMedicalId);
    
    Page<RendezVousMedical> findByStaffMedicalIdOrderByDateRendezVousAscHeureRendezVousAsc(Long staffMedicalId, Pageable pageable);

    // Recherches par date
    List<RendezVousMedical> findByDateRendezVousOrderByHeureRendezVousAsc(LocalDate dateRendezVous);
    
    List<RendezVousMedical> findByDateRendezVousBetweenOrderByDateRendezVousAscHeureRendezVousAsc(
            LocalDate dateDebut, LocalDate dateFin);
    
    List<RendezVousMedical> findByStaffMedicalIdAndDateRendezVousOrderByHeureRendezVousAsc(
            Long staffMedicalId, LocalDate dateRendezVous);

    // Recherches par statut
    List<RendezVousMedical> findByStatutOrderByDateRendezVousAscHeureRendezVousAsc(String statut);
    
    List<RendezVousMedical> findByJoueurIdAndStatutOrderByDateRendezVousAscHeureRendezVousAsc(
            Long joueurId, String statut);
    
    List<RendezVousMedical> findByStaffMedicalIdAndStatutOrderByDateRendezVousAscHeureRendezVousAsc(
            Long staffMedicalId, String statut);

    // Recherches par type
    List<RendezVousMedical> findByTypeRendezVousOrderByDateRendezVousAscHeureRendezVousAsc(String typeRendezVous);
    
    List<RendezVousMedical> findByJoueurIdAndTypeRendezVousOrderByDateRendezVousAscHeureRendezVousAsc(
            Long joueurId, String typeRendezVous);

    // Recherches par priorité
    List<RendezVousMedical> findByPrioriteOrderByDateRendezVousAscHeureRendezVousAsc(String priorite);
    
    @Query("SELECT r FROM RendezVousMedical r WHERE r.priorite IN ('ELEVEE', 'URGENTE') ORDER BY r.dateRendezVous ASC, r.heureRendezVous ASC")
    List<RendezVousMedical> findRendezVousUrgents();

    // Recherches pour aujourd'hui
    @Query("SELECT r FROM RendezVousMedical r WHERE r.dateRendezVous = CURRENT_DATE ORDER BY r.heureRendezVous ASC")
    List<RendezVousMedical> findRendezVousAujourdhui();
    
    @Query("SELECT r FROM RendezVousMedical r WHERE r.staffMedicalId = :staffMedicalId AND r.dateRendezVous = CURRENT_DATE ORDER BY r.heureRendezVous ASC")
    List<RendezVousMedical> findRendezVousAujourdhuiParStaff(@Param("staffMedicalId") Long staffMedicalId);
    
    @Query("SELECT r FROM RendezVousMedical r WHERE r.joueurId = :joueurId AND r.dateRendezVous = CURRENT_DATE ORDER BY r.heureRendezVous ASC")
    List<RendezVousMedical> findRendezVousAujourdhuiParJoueur(@Param("joueurId") Long joueurId);

    // Recherches pour demain
    @Query("SELECT r FROM RendezVousMedical r WHERE r.dateRendezVous = CURRENT_DATE + 1 ORDER BY r.heureRendezVous ASC")
    List<RendezVousMedical> findRendezVousDemain();
    
    @Query("SELECT r FROM RendezVousMedical r WHERE r.staffMedicalId = :staffMedicalId AND r.dateRendezVous = CURRENT_DATE + 1 ORDER BY r.heureRendezVous ASC")
    List<RendezVousMedical> findRendezVousDemainParStaff(@Param("staffMedicalId") Long staffMedicalId);

    // Recherches pour la semaine
    @Query("SELECT r FROM RendezVousMedical r WHERE r.dateRendezVous BETWEEN CURRENT_DATE AND CURRENT_DATE + 7 ORDER BY r.dateRendezVous ASC, r.heureRendezVous ASC")
    List<RendezVousMedical> findRendezVousSemaineProchaine();
    
    @Query("SELECT r FROM RendezVousMedical r WHERE r.staffMedicalId = :staffMedicalId AND r.dateRendezVous BETWEEN CURRENT_DATE AND CURRENT_DATE + 7 ORDER BY r.dateRendezVous ASC, r.heureRendezVous ASC")
    List<RendezVousMedical> findRendezVousSemaineProchainePourStaff(@Param("staffMedicalId") Long staffMedicalId);

    // Recherches pour rappels
    @Query("SELECT r FROM RendezVousMedical r WHERE r.rappelEnvoye = false AND r.dateRendezVous = CURRENT_DATE + 1 AND r.statut IN ('PLANIFIE', 'CONFIRME') ORDER BY r.heureRendezVous ASC")
    List<RendezVousMedical> findRendezVousNecessitantRappel();
    
    @Query("SELECT r FROM RendezVousMedical r WHERE r.rappelEnvoye = false AND r.dateRendezVous BETWEEN CURRENT_DATE AND CURRENT_DATE + 1 AND r.statut IN ('PLANIFIE', 'CONFIRME') ORDER BY r.dateRendezVous ASC, r.heureRendezVous ASC")
    List<RendezVousMedical> findRendezVousProchesNecessitantRappel();

    // Vérifications de disponibilité
    @Query("SELECT r FROM RendezVousMedical r WHERE r.staffMedicalId = :staffMedicalId AND r.dateRendezVous = :date AND r.heureRendezVous BETWEEN :heureDebut AND :heureFin AND r.statut NOT IN ('ANNULE', 'TERMINE')")
    List<RendezVousMedical> findConflitsCreneauStaff(
            @Param("staffMedicalId") Long staffMedicalId,
            @Param("date") LocalDate date,
            @Param("heureDebut") LocalTime heureDebut,
            @Param("heureFin") LocalTime heureFin);
    
    @Query("SELECT r FROM RendezVousMedical r WHERE r.joueurId = :joueurId AND r.dateRendezVous = :date AND r.heureRendezVous BETWEEN :heureDebut AND :heureFin AND r.statut NOT IN ('ANNULE', 'TERMINE')")
    List<RendezVousMedical> findConflitsCreneauJoueur(
            @Param("joueurId") Long joueurId,
            @Param("date") LocalDate date,
            @Param("heureDebut") LocalTime heureDebut,
            @Param("heureFin") LocalTime heureFin);

    // Statistiques
    long countByJoueurId(Long joueurId);
    
    long countByStaffMedicalId(Long staffMedicalId);
    
    long countByStatut(String statut);
    
    long countByTypeRendezVous(String typeRendezVous);
    
    long countByPriorite(String priorite);

    // Statistiques par période
    @Query("SELECT COUNT(r) FROM RendezVousMedical r WHERE r.dateRendezVous BETWEEN :dateDebut AND :dateFin")
    long countBetweenDates(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);
    
    @Query("SELECT r.statut, COUNT(r) FROM RendezVousMedical r WHERE r.dateRendezVous BETWEEN :dateDebut AND :dateFin GROUP BY r.statut")
    List<Object[]> countByStatutBetweenDates(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);
    
    @Query("SELECT r.typeRendezVous, COUNT(r) FROM RendezVousMedical r WHERE r.dateRendezVous BETWEEN :dateDebut AND :dateFin GROUP BY r.typeRendezVous")
    List<Object[]> countByTypeBetweenDates(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);

    // Recherches pour rapports
    @Query("SELECT r.joueurId, COUNT(r), " +
           "SUM(CASE WHEN r.statut = 'TERMINE' THEN 1 ELSE 0 END) as termines, " +
           "SUM(CASE WHEN r.statut = 'ANNULE' THEN 1 ELSE 0 END) as annules " +
           "FROM RendezVousMedical r " +
           "WHERE r.dateRendezVous BETWEEN :dateDebut AND :dateFin " +
           "GROUP BY r.joueurId " +
           "ORDER BY COUNT(r) DESC")
    List<Object[]> getStatistiquesParJoueur(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);
    
    @Query("SELECT r.staffMedicalId, COUNT(r), " +
           "AVG(r.dureeReelle) as dureeMoyenne, " +
           "SUM(CASE WHEN r.statut = 'TERMINE' THEN 1 ELSE 0 END) as termines " +
           "FROM RendezVousMedical r " +
           "WHERE r.dateRendezVous BETWEEN :dateDebut AND :dateFin AND r.dureeReelle IS NOT NULL " +
           "GROUP BY r.staffMedicalId")
    List<Object[]> getStatistiquesParStaffMedical(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);

    // Recherches pour dashboard
    @Query("SELECT COUNT(r) FROM RendezVousMedical r WHERE r.dateRendezVous = CURRENT_DATE")
    long countRendezVousAujourdhui();
    
    @Query("SELECT COUNT(r) FROM RendezVousMedical r WHERE r.dateRendezVous = CURRENT_DATE + 1")
    long countRendezVousDemain();
    
    @Query("SELECT COUNT(r) FROM RendezVousMedical r WHERE r.dateRendezVous BETWEEN CURRENT_DATE AND CURRENT_DATE + 7")
    long countRendezVousSemaineProchaine();

    // Recherches pour alertes
    @Query("SELECT r FROM RendezVousMedical r WHERE r.dateRendezVous < CURRENT_DATE AND r.statut IN ('PLANIFIE', 'CONFIRME') ORDER BY r.dateRendezVous DESC")
    List<RendezVousMedical> findRendezVousEchus();
    
    @Query("SELECT r FROM RendezVousMedical r WHERE r.prochaineVisite IS NOT NULL AND r.prochaineVisite <= CURRENT_DATE ORDER BY r.prochaineVisite")
    List<RendezVousMedical> findProchainesVisitesEchues();

    // Recherches complexes avec filtres
    @Query("SELECT r FROM RendezVousMedical r WHERE " +
           "(:joueurId IS NULL OR r.joueurId = :joueurId) AND " +
           "(:staffMedicalId IS NULL OR r.staffMedicalId = :staffMedicalId) AND " +
           "(:typeRendezVous IS NULL OR r.typeRendezVous = :typeRendezVous) AND " +
           "(:statut IS NULL OR r.statut = :statut) AND " +
           "(:priorite IS NULL OR r.priorite = :priorite) AND " +
           "(:dateDebut IS NULL OR r.dateRendezVous >= :dateDebut) AND " +
           "(:dateFin IS NULL OR r.dateRendezVous <= :dateFin) " +
           "ORDER BY r.dateRendezVous ASC, r.heureRendezVous ASC")
    Page<RendezVousMedical> findWithFilters(
            @Param("joueurId") Long joueurId,
            @Param("staffMedicalId") Long staffMedicalId,
            @Param("typeRendezVous") String typeRendezVous,
            @Param("statut") String statut,
            @Param("priorite") String priorite,
            @Param("dateDebut") LocalDate dateDebut,
            @Param("dateFin") LocalDate dateFin,
            Pageable pageable);

    // Recherche textuelle
    @Query("SELECT r FROM RendezVousMedical r WHERE " +
           "LOWER(r.motif) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(r.notes) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(r.compteRendu) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(r.lieu) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "ORDER BY r.dateRendezVous DESC, r.heureRendezVous DESC")
    List<RendezVousMedical> searchByText(@Param("searchTerm") String searchTerm);

    // Recherches pour planning
    @Query("SELECT r FROM RendezVousMedical r WHERE r.staffMedicalId = :staffMedicalId AND r.dateRendezVous BETWEEN :dateDebut AND :dateFin AND r.statut NOT IN ('ANNULE') ORDER BY r.dateRendezVous ASC, r.heureRendezVous ASC")
    List<RendezVousMedical> findPlanningStaffMedical(
            @Param("staffMedicalId") Long staffMedicalId,
            @Param("dateDebut") LocalDate dateDebut,
            @Param("dateFin") LocalDate dateFin);
    
    @Query("SELECT r FROM RendezVousMedical r WHERE r.joueurId = :joueurId AND r.dateRendezVous BETWEEN :dateDebut AND :dateFin AND r.statut NOT IN ('ANNULE') ORDER BY r.dateRendezVous ASC, r.heureRendezVous ASC")
    List<RendezVousMedical> findPlanningJoueur(
            @Param("joueurId") Long joueurId,
            @Param("dateDebut") LocalDate dateDebut,
            @Param("dateFin") LocalDate dateFin);
}
