package com.sprintbot.medicaladmin.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Entité représentant une demande administrative interne
 * Gère le workflow de validation des demandes administratives
 */
@Entity
@Table(name = "demandes_administratives")
@EntityListeners(AuditingEntityListener.class)
public class DemandeAdministrative {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "L'ID du demandeur est obligatoire")
    @Column(name = "demandeur_id", nullable = false)
    private Long demandeurId;

    @NotBlank(message = "Le type de demandeur est obligatoire")
    @Size(max = 50, message = "Le type de demandeur ne peut pas dépasser 50 caractères")
    @Column(name = "type_demandeur", nullable = false, length = 50)
    private String typeDemandeur; // JOUEUR, COACH, STAFF_MEDICAL, RESPONSABLE_FINANCIER

    @Column(name = "approbateur_id")
    private Long approvateurId; // ID de l'administrateur qui traite

    @NotBlank(message = "Le type de demande est obligatoire")
    @Size(max = 100, message = "Le type de demande ne peut pas dépasser 100 caractères")
    @Column(name = "type_demande", nullable = false, length = 100)
    private String typeDemande; // CONGE, MATERIEL, FORMATION, REMBOURSEMENT, ACCES, AUTRE

    @NotBlank(message = "Le titre est obligatoire")
    @Size(max = 200, message = "Le titre ne peut pas dépasser 200 caractères")
    @Column(name = "titre", nullable = false, length = 200)
    private String titre;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "justification", columnDefinition = "TEXT")
    private String justification;

    @Size(max = 50, message = "La priorité ne peut pas dépasser 50 caractères")
    @Column(name = "priorite", length = 50)
    private String priorite = "NORMALE"; // FAIBLE, NORMALE, ELEVEE, URGENTE

    @Size(max = 50, message = "Le statut ne peut pas dépasser 50 caractères")
    @Column(name = "statut", length = 50)
    private String statut = "EN_ATTENTE"; // EN_ATTENTE, EN_TRAITEMENT, VALIDEE, REJETEE, SUSPENDUE

    @NotNull(message = "La date de soumission est obligatoire")
    @Column(name = "date_soumission", nullable = false)
    private LocalDate dateSoumission;

    @Column(name = "date_traitement")
    private LocalDate dateTraitement;

    @Column(name = "date_echeance")
    private LocalDate dateEcheance;

    @Column(name = "commentaire_approbateur", columnDefinition = "TEXT")
    private String commentaireApprobateur;

    @Column(name = "documents_joints", columnDefinition = "TEXT")
    private String documentsJoints; // URLs ou chemins des fichiers

    @Column(name = "cout_estime")
    private Double coutEstime;

    @Column(name = "cout_reel")
    private Double coutReel;

    @Size(max = 100, message = "La catégorie budgétaire ne peut pas dépasser 100 caractères")
    @Column(name = "categorie_budgetaire", length = 100)
    private String categorieBudgetaire;

    @Column(name = "impact_planning")
    private Boolean impactPlanning = false;

    @Column(name = "necessite_validation_coach")
    private Boolean necessiteValidationCoach = false;

    @Column(name = "necessite_validation_medical")
    private Boolean necessiteValidationMedical = false;

    @Column(name = "necessite_validation_financier")
    private Boolean necessiteValidationFinancier = false;

    @Column(name = "validation_coach")
    private Boolean validationCoach;

    @Column(name = "validation_medical")
    private Boolean validationMedical;

    @Column(name = "validation_financier")
    private Boolean validationFinancier;

    @CreatedDate
    @Column(name = "date_creation", nullable = false, updatable = false)
    private LocalDateTime dateCreation;

    @LastModifiedDate
    @Column(name = "date_modification")
    private LocalDateTime dateModification;

    // Constructeurs
    public DemandeAdministrative() {
        this.dateSoumission = LocalDate.now();
        this.statut = "EN_ATTENTE";
        this.priorite = "NORMALE";
        this.impactPlanning = false;
        this.necessiteValidationCoach = false;
        this.necessiteValidationMedical = false;
        this.necessiteValidationFinancier = false;
    }

    public DemandeAdministrative(Long demandeurId, String typeDemandeur, String typeDemande, 
                               String titre, String description) {
        this();
        this.demandeurId = demandeurId;
        this.typeDemandeur = typeDemandeur;
        this.typeDemande = typeDemande;
        this.titre = titre;
        this.description = description;
    }

    // Méthodes métier
    public void soumettre() {
        this.dateSoumission = LocalDate.now();
        this.statut = "EN_ATTENTE";
    }

    public void commencerTraitement(Long approvateurId) {
        this.statut = "EN_TRAITEMENT";
        this.approvateurId = approvateurId;
        this.dateTraitement = LocalDate.now();
    }

    public void valider(String commentaire) {
        this.statut = "VALIDEE";
        this.commentaireApprobateur = commentaire;
        this.dateTraitement = LocalDate.now();
    }

    public void rejeter(String commentaire) {
        this.statut = "REJETEE";
        this.commentaireApprobateur = commentaire;
        this.dateTraitement = LocalDate.now();
    }

    public void suspendre(String commentaire) {
        this.statut = "SUSPENDUE";
        this.commentaireApprobateur = commentaire;
    }

    public boolean estEnAttente() {
        return "EN_ATTENTE".equals(this.statut);
    }

    public boolean estValidee() {
        return "VALIDEE".equals(this.statut);
    }

    public boolean estRejetee() {
        return "REJETEE".equals(this.statut);
    }

    public boolean estEnTraitement() {
        return "EN_TRAITEMENT".equals(this.statut);
    }

    public boolean estUrgente() {
        return "URGENTE".equals(this.priorite);
    }

    public boolean estEchue() {
        return this.dateEcheance != null && this.dateEcheance.isBefore(LocalDate.now());
    }

    public long getNombreJoursDepuisSoumission() {
        return this.dateSoumission.until(LocalDate.now()).getDays();
    }

    public long getNombreJoursAvantEcheance() {
        if (this.dateEcheance == null) {
            return Long.MAX_VALUE;
        }
        return LocalDate.now().until(this.dateEcheance).getDays();
    }

    public boolean necessiteValidationsMultiples() {
        return this.necessiteValidationCoach || this.necessiteValidationMedical || 
               this.necessiteValidationFinancier;
    }

    public boolean toutesValidationsObtenues() {
        if (!necessiteValidationsMultiples()) {
            return true;
        }
        
        boolean coachOk = !this.necessiteValidationCoach || Boolean.TRUE.equals(this.validationCoach);
        boolean medicalOk = !this.necessiteValidationMedical || Boolean.TRUE.equals(this.validationMedical);
        boolean financierOk = !this.necessiteValidationFinancier || Boolean.TRUE.equals(this.validationFinancier);
        
        return coachOk && medicalOk && financierOk;
    }

    public void definirValidationsNecessaires(String typeDemande) {
        switch (typeDemande.toUpperCase()) {
            case "CONGE":
                this.necessiteValidationCoach = true;
                this.impactPlanning = true;
                break;
            case "MATERIEL":
                this.necessiteValidationFinancier = true;
                break;
            case "FORMATION":
                this.necessiteValidationCoach = true;
                this.necessiteValidationFinancier = true;
                break;
            case "REMBOURSEMENT":
                this.necessiteValidationFinancier = true;
                break;
            case "ACCES":
                // Pas de validation spéciale nécessaire
                break;
            default:
                // Pour les autres types, évaluation au cas par cas
                break;
        }
    }

    // Getters et Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getDemandeurId() { return demandeurId; }
    public void setDemandeurId(Long demandeurId) { this.demandeurId = demandeurId; }

    public String getTypeDemandeur() { return typeDemandeur; }
    public void setTypeDemandeur(String typeDemandeur) { this.typeDemandeur = typeDemandeur; }

    public Long getApprovateurId() { return approvateurId; }
    public void setApprovateurId(Long approvateurId) { this.approvateurId = approvateurId; }

    public String getTypeDemande() { return typeDemande; }
    public void setTypeDemande(String typeDemande) { this.typeDemande = typeDemande; }

    public String getTitre() { return titre; }
    public void setTitre(String titre) { this.titre = titre; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getJustification() { return justification; }
    public void setJustification(String justification) { this.justification = justification; }

    public String getPriorite() { return priorite; }
    public void setPriorite(String priorite) { this.priorite = priorite; }

    public String getStatut() { return statut; }
    public void setStatut(String statut) { this.statut = statut; }

    public LocalDate getDateSoumission() { return dateSoumission; }
    public void setDateSoumission(LocalDate dateSoumission) { this.dateSoumission = dateSoumission; }

    public LocalDate getDateTraitement() { return dateTraitement; }
    public void setDateTraitement(LocalDate dateTraitement) { this.dateTraitement = dateTraitement; }

    public LocalDate getDateEcheance() { return dateEcheance; }
    public void setDateEcheance(LocalDate dateEcheance) { this.dateEcheance = dateEcheance; }

    public String getCommentaireApprobateur() { return commentaireApprobateur; }
    public void setCommentaireApprobateur(String commentaireApprobateur) { this.commentaireApprobateur = commentaireApprobateur; }

    public String getDocumentsJoints() { return documentsJoints; }
    public void setDocumentsJoints(String documentsJoints) { this.documentsJoints = documentsJoints; }

    public Double getCoutEstime() { return coutEstime; }
    public void setCoutEstime(Double coutEstime) { this.coutEstime = coutEstime; }

    public Double getCoutReel() { return coutReel; }
    public void setCoutReel(Double coutReel) { this.coutReel = coutReel; }

    public String getCategorieBudgetaire() { return categorieBudgetaire; }
    public void setCategorieBudgetaire(String categorieBudgetaire) { this.categorieBudgetaire = categorieBudgetaire; }

    public Boolean getImpactPlanning() { return impactPlanning; }
    public void setImpactPlanning(Boolean impactPlanning) { this.impactPlanning = impactPlanning; }

    public Boolean getNecessiteValidationCoach() { return necessiteValidationCoach; }
    public void setNecessiteValidationCoach(Boolean necessiteValidationCoach) { this.necessiteValidationCoach = necessiteValidationCoach; }

    public Boolean getNecessiteValidationMedical() { return necessiteValidationMedical; }
    public void setNecessiteValidationMedical(Boolean necessiteValidationMedical) { this.necessiteValidationMedical = necessiteValidationMedical; }

    public Boolean getNecessiteValidationFinancier() { return necessiteValidationFinancier; }
    public void setNecessiteValidationFinancier(Boolean necessiteValidationFinancier) { this.necessiteValidationFinancier = necessiteValidationFinancier; }

    public Boolean getValidationCoach() { return validationCoach; }
    public void setValidationCoach(Boolean validationCoach) { this.validationCoach = validationCoach; }

    public Boolean getValidationMedical() { return validationMedical; }
    public void setValidationMedical(Boolean validationMedical) { this.validationMedical = validationMedical; }

    public Boolean getValidationFinancier() { return validationFinancier; }
    public void setValidationFinancier(Boolean validationFinancier) { this.validationFinancier = validationFinancier; }

    public LocalDateTime getDateCreation() { return dateCreation; }
    public void setDateCreation(LocalDateTime dateCreation) { this.dateCreation = dateCreation; }

    public LocalDateTime getDateModification() { return dateModification; }
    public void setDateModification(LocalDateTime dateModification) { this.dateModification = dateModification; }

    @Override
    public String toString() {
        return "DemandeAdministrative{" +
                "id=" + id +
                ", demandeurId=" + demandeurId +
                ", typeDemande='" + typeDemande + '\'' +
                ", titre='" + titre + '\'' +
                ", statut='" + statut + '\'' +
                ", dateSoumission=" + dateSoumission +
                '}';
    }
}
