package com.sprintbot.medicaladmin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Application principale du microservice Medical-Admin-Service
 * 
 * Ce microservice gère :
 * - Les données de santé des joueurs
 * - Les rendez-vous médicaux
 * - Les demandes administratives internes
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableTransactionManagement
public class MedicalAdminServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(MedicalAdminServiceApplication.class, args);
        
        System.out.println("🏥 ========================================");
        System.out.println("🏥   MEDICAL-ADMIN-SERVICE DÉMARRÉ");
        System.out.println("🏥 ========================================");
        System.out.println("🏥");
        System.out.println("🏥 📊 Fonctionnalités disponibles :");
        System.out.println("🏥   ✅ Gestion des données de santé");
        System.out.println("🏥   ✅ Rendez-vous médicaux");
        System.out.println("🏥   ✅ Demandes administratives");
        System.out.println("🏥");
        System.out.println("🏥 🔗 Endpoints principaux :");
        System.out.println("🏥   📡 API: http://localhost:8083/api");
        System.out.println("🏥   📚 Docs: http://localhost:8083/swagger-ui.html");
        System.out.println("🏥   ❤️ Health: http://localhost:8083/actuator/health");
        System.out.println("🏥");
        System.out.println("🏥 ========================================");
    }
}
