package com.sprintbot.medicaladmin.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Entité représentant un rendez-vous médical
 * Gère la planification et le suivi des consultations médicales
 */
@Entity
@Table(name = "rendez_vous_medicaux")
@EntityListeners(AuditingEntityListener.class)
public class RendezVousMedical {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "L'ID du joueur est obligatoire")
    @Column(name = "joueur_id", nullable = false)
    private Long joueurId;

    @NotNull(message = "L'ID du staff médical est obligatoire")
    @Column(name = "staff_medical_id", nullable = false)
    private Long staffMedicalId;

    @NotNull(message = "La date du rendez-vous est obligatoire")
    @Column(name = "date_rendez_vous", nullable = false)
    private LocalDate dateRendezVous;

    @NotNull(message = "L'heure du rendez-vous est obligatoire")
    @Column(name = "heure_rendez_vous", nullable = false)
    private LocalTime heureRendezVous;

    @NotBlank(message = "Le type de rendez-vous est obligatoire")
    @Size(max = 100, message = "Le type ne peut pas dépasser 100 caractères")
    @Column(name = "type_rendez_vous", nullable = false, length = 100)
    private String typeRendezVous; // CONSULTATION, SUIVI, URGENCE, BILAN, REEDUCATION

    @Size(max = 200, message = "Le lieu ne peut pas dépasser 200 caractères")
    @Column(name = "lieu", length = 200)
    private String lieu;

    @Column(name = "motif", columnDefinition = "TEXT")
    private String motif;

    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    @Size(max = 50, message = "Le statut ne peut pas dépasser 50 caractères")
    @Column(name = "statut", length = 50)
    private String statut = "PLANIFIE"; // PLANIFIE, CONFIRME, EN_COURS, TERMINE, ANNULE, REPORTE

    @Column(name = "duree_prevue")
    private Integer dureePrevue; // en minutes

    @Column(name = "duree_reelle")
    private Integer dureeReelle; // en minutes

    @Size(max = 50, message = "La priorité ne peut pas dépasser 50 caractères")
    @Column(name = "priorite", length = 50)
    private String priorite = "NORMALE"; // FAIBLE, NORMALE, ELEVEE, URGENTE

    @Column(name = "rappel_envoye")
    private Boolean rappelEnvoye = false;

    @Column(name = "date_rappel")
    private LocalDateTime dateRappel;

    @Column(name = "compte_rendu", columnDefinition = "TEXT")
    private String compteRendu;

    @Column(name = "prescriptions", columnDefinition = "TEXT")
    private String prescriptions;

    @Column(name = "prochaine_visite")
    private LocalDate prochaineVisite;

    @CreatedDate
    @Column(name = "date_creation", nullable = false, updatable = false)
    private LocalDateTime dateCreation;

    @LastModifiedDate
    @Column(name = "date_modification")
    private LocalDateTime dateModification;

    // Relations
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "donnees_sante_id")
    private DonneesSante donneesSante;

    // Constructeurs
    public RendezVousMedical() {
        this.statut = "PLANIFIE";
        this.priorite = "NORMALE";
        this.rappelEnvoye = false;
    }

    public RendezVousMedical(Long joueurId, Long staffMedicalId, LocalDate dateRendezVous, 
                           LocalTime heureRendezVous, String typeRendezVous) {
        this();
        this.joueurId = joueurId;
        this.staffMedicalId = staffMedicalId;
        this.dateRendezVous = dateRendezVous;
        this.heureRendezVous = heureRendezVous;
        this.typeRendezVous = typeRendezVous;
    }

    // Méthodes métier
    public void confirmer() {
        this.statut = "CONFIRME";
    }

    public void commencer() {
        this.statut = "EN_COURS";
    }

    public void terminer() {
        this.statut = "TERMINE";
    }

    public void annuler() {
        this.statut = "ANNULE";
    }

    public void reporter(LocalDate nouvelleDate, LocalTime nouvelleHeure) {
        this.statut = "REPORTE";
        this.dateRendezVous = nouvelleDate;
        this.heureRendezVous = nouvelleHeure;
    }

    public void marquerRappelEnvoye() {
        this.rappelEnvoye = true;
        this.dateRappel = LocalDateTime.now();
    }

    public boolean estPasse() {
        LocalDateTime maintenant = LocalDateTime.now();
        LocalDateTime dateTimeRendezVous = LocalDateTime.of(this.dateRendezVous, this.heureRendezVous);
        return dateTimeRendezVous.isBefore(maintenant);
    }

    public boolean estAujourdhui() {
        return this.dateRendezVous.equals(LocalDate.now());
    }

    public boolean estDansLesProchaines24h() {
        LocalDateTime maintenant = LocalDateTime.now();
        LocalDateTime dateTimeRendezVous = LocalDateTime.of(this.dateRendezVous, this.heureRendezVous);
        return dateTimeRendezVous.isAfter(maintenant) && 
               dateTimeRendezVous.isBefore(maintenant.plusDays(1));
    }

    public boolean necessiteRappel() {
        return !this.rappelEnvoye && estDansLesProchaines24h() && 
               ("PLANIFIE".equals(this.statut) || "CONFIRME".equals(this.statut));
    }

    public boolean estUrgent() {
        return "URGENTE".equals(this.priorite) || "URGENCE".equals(this.typeRendezVous);
    }

    public long getMinutesAvantRendezVous() {
        LocalDateTime maintenant = LocalDateTime.now();
        LocalDateTime dateTimeRendezVous = LocalDateTime.of(this.dateRendezVous, this.heureRendezVous);
        
        if (dateTimeRendezVous.isBefore(maintenant)) {
            return 0;
        }
        
        return java.time.Duration.between(maintenant, dateTimeRendezVous).toMinutes();
    }

    // Getters et Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getJoueurId() { return joueurId; }
    public void setJoueurId(Long joueurId) { this.joueurId = joueurId; }

    public Long getStaffMedicalId() { return staffMedicalId; }
    public void setStaffMedicalId(Long staffMedicalId) { this.staffMedicalId = staffMedicalId; }

    public LocalDate getDateRendezVous() { return dateRendezVous; }
    public void setDateRendezVous(LocalDate dateRendezVous) { this.dateRendezVous = dateRendezVous; }

    public LocalTime getHeureRendezVous() { return heureRendezVous; }
    public void setHeureRendezVous(LocalTime heureRendezVous) { this.heureRendezVous = heureRendezVous; }

    public String getTypeRendezVous() { return typeRendezVous; }
    public void setTypeRendezVous(String typeRendezVous) { this.typeRendezVous = typeRendezVous; }

    public String getLieu() { return lieu; }
    public void setLieu(String lieu) { this.lieu = lieu; }

    public String getMotif() { return motif; }
    public void setMotif(String motif) { this.motif = motif; }

    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }

    public String getStatut() { return statut; }
    public void setStatut(String statut) { this.statut = statut; }

    public Integer getDureePrevue() { return dureePrevue; }
    public void setDureePrevue(Integer dureePrevue) { this.dureePrevue = dureePrevue; }

    public Integer getDureeReelle() { return dureeReelle; }
    public void setDureeReelle(Integer dureeReelle) { this.dureeReelle = dureeReelle; }

    public String getPriorite() { return priorite; }
    public void setPriorite(String priorite) { this.priorite = priorite; }

    public Boolean getRappelEnvoye() { return rappelEnvoye; }
    public void setRappelEnvoye(Boolean rappelEnvoye) { this.rappelEnvoye = rappelEnvoye; }

    public LocalDateTime getDateRappel() { return dateRappel; }
    public void setDateRappel(LocalDateTime dateRappel) { this.dateRappel = dateRappel; }

    public String getCompteRendu() { return compteRendu; }
    public void setCompteRendu(String compteRendu) { this.compteRendu = compteRendu; }

    public String getPrescriptions() { return prescriptions; }
    public void setPrescriptions(String prescriptions) { this.prescriptions = prescriptions; }

    public LocalDate getProchaineVisite() { return prochaineVisite; }
    public void setProchaineVisite(LocalDate prochaineVisite) { this.prochaineVisite = prochaineVisite; }

    public LocalDateTime getDateCreation() { return dateCreation; }
    public void setDateCreation(LocalDateTime dateCreation) { this.dateCreation = dateCreation; }

    public LocalDateTime getDateModification() { return dateModification; }
    public void setDateModification(LocalDateTime dateModification) { this.dateModification = dateModification; }

    public DonneesSante getDonneesSante() { return donneesSante; }
    public void setDonneesSante(DonneesSante donneesSante) { this.donneesSante = donneesSante; }

    @Override
    public String toString() {
        return "RendezVousMedical{" +
                "id=" + id +
                ", joueurId=" + joueurId +
                ", dateRendezVous=" + dateRendezVous +
                ", heureRendezVous=" + heureRendezVous +
                ", typeRendezVous='" + typeRendezVous + '\'' +
                ", statut='" + statut + '\'' +
                '}';
    }
}
