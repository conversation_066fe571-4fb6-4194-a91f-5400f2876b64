package com.sprintbot.medicaladmin.repository;

import com.sprintbot.medicaladmin.entity.DonneesSante;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Repository pour la gestion des données de santé
 * Fournit les opérations CRUD et requêtes personnalisées
 */
@Repository
public interface DonneesSanteRepository extends JpaRepository<DonneesSante, Long> {

    // Recherches par joueur
    List<DonneesSante> findByJoueurIdOrderByDateExamenDesc(Long joueurId);
    
    Page<DonneesSante> findByJoueurIdOrderByDateExamenDesc(Long joueurId, Pageable pageable);
    
    Optional<DonneesSante> findFirstByJoueurIdOrderByDateExamenDesc(Long joueurId);

    // Recherches par staff médical
    List<DonneesSante> findByStaffMedicalIdOrderByDateExamenDesc(Long staffMedicalId);
    
    Page<DonneesSante> findByStaffMedicalIdOrderByDateExamenDesc(Long staffMedicalId, Pageable pageable);

    // Recherches par type d'examen
    List<DonneesSante> findByTypeExamenOrderByDateExamenDesc(String typeExamen);
    
    List<DonneesSante> findByJoueurIdAndTypeExamenOrderByDateExamenDesc(Long joueurId, String typeExamen);

    // Recherches par statut
    List<DonneesSante> findByStatutOrderByDateExamenDesc(String statut);
    
    List<DonneesSante> findByJoueurIdAndStatutOrderByDateExamenDesc(Long joueurId, String statut);

    // Recherches par gravité
    List<DonneesSante> findByGraviteOrderByDateExamenDesc(String gravite);
    
    @Query("SELECT d FROM DonneesSante d WHERE d.gravite IN ('GRAVE', 'CRITIQUE') ORDER BY d.dateExamen DESC")
    List<DonneesSante> findDonneesGraves();

    // Recherches par date
    List<DonneesSante> findByDateExamen(LocalDate dateExamen);
    
    List<DonneesSante> findByDateExamenBetweenOrderByDateExamenDesc(LocalDate dateDebut, LocalDate dateFin);
    
    List<DonneesSante> findByJoueurIdAndDateExamenBetweenOrderByDateExamenDesc(
            Long joueurId, LocalDate dateDebut, LocalDate dateFin);

    // Recherches avec blessures
    @Query("SELECT d FROM DonneesSante d WHERE d.blessures IS NOT NULL AND d.blessures != '' ORDER BY d.dateExamen DESC")
    List<DonneesSante> findDonneesAvecBlessures();
    
    @Query("SELECT d FROM DonneesSante d WHERE d.joueurId = :joueurId AND d.blessures IS NOT NULL AND d.blessures != '' ORDER BY d.dateExamen DESC")
    List<DonneesSante> findBlessuresParJoueur(@Param("joueurId") Long joueurId);

    // Recherches récentes
    @Query("SELECT d FROM DonneesSante d WHERE d.dateExamen >= :dateDebut ORDER BY d.dateExamen DESC")
    List<DonneesSante> findDonneesRecentes(@Param("dateDebut") LocalDate dateDebut);
    
    @Query("SELECT d FROM DonneesSante d WHERE d.joueurId = :joueurId AND d.dateExamen >= :dateDebut ORDER BY d.dateExamen DESC")
    List<DonneesSante> findDonneesRecentesParJoueur(@Param("joueurId") Long joueurId, @Param("dateDebut") LocalDate dateDebut);

    // Recherches nécessitant un suivi
    @Query("SELECT d FROM DonneesSante d WHERE d.statut IN ('EN_TRAITEMENT', 'SUIVI') ORDER BY d.dateExamen DESC")
    List<DonneesSante> findDonneesNecessitantSuivi();
    
    @Query("SELECT d FROM DonneesSante d WHERE d.joueurId = :joueurId AND d.statut IN ('EN_TRAITEMENT', 'SUIVI') ORDER BY d.dateExamen DESC")
    List<DonneesSante> findSuiviParJoueur(@Param("joueurId") Long joueurId);

    // Statistiques
    long countByJoueurId(Long joueurId);
    
    long countByStaffMedicalId(Long staffMedicalId);
    
    long countByStatut(String statut);
    
    long countByGravite(String gravite);
    
    @Query("SELECT COUNT(d) FROM DonneesSante d WHERE d.blessures IS NOT NULL AND d.blessures != ''")
    long countDonneesAvecBlessures();
    
    @Query("SELECT COUNT(d) FROM DonneesSante d WHERE d.joueurId = :joueurId AND d.blessures IS NOT NULL AND d.blessures != ''")
    long countBlessuresParJoueur(@Param("joueurId") Long joueurId);

    // Recherches par période avec statistiques
    @Query("SELECT d.typeExamen, COUNT(d) FROM DonneesSante d WHERE d.dateExamen BETWEEN :dateDebut AND :dateFin GROUP BY d.typeExamen")
    List<Object[]> countByTypeExamenBetweenDates(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);
    
    @Query("SELECT d.gravite, COUNT(d) FROM DonneesSante d WHERE d.dateExamen BETWEEN :dateDebut AND :dateFin AND d.gravite IS NOT NULL GROUP BY d.gravite")
    List<Object[]> countByGraviteBetweenDates(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);

    // Recherches pour alertes
    @Query("SELECT d FROM DonneesSante d WHERE d.dateGuerisonPrevue IS NOT NULL AND d.dateGuerisonPrevue <= :date AND d.statut != 'GUERI' ORDER BY d.dateGuerisonPrevue")
    List<DonneesSante> findGuerisonsPrevuesEchues(@Param("date") LocalDate date);
    
    @Query("SELECT d FROM DonneesSante d WHERE d.dateGuerisonPrevue IS NOT NULL AND d.dateGuerisonPrevue BETWEEN :dateDebut AND :dateFin AND d.statut != 'GUERI' ORDER BY d.dateGuerisonPrevue")
    List<DonneesSante> findGuerisonsPrevuesProchaines(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);

    // Recherches pour rapports
    @Query("SELECT d.joueurId, COUNT(d), " +
           "SUM(CASE WHEN d.blessures IS NOT NULL AND d.blessures != '' THEN 1 ELSE 0 END) as nombreBlessures, " +
           "SUM(CASE WHEN d.gravite IN ('GRAVE', 'CRITIQUE') THEN 1 ELSE 0 END) as nombreGraves " +
           "FROM DonneesSante d " +
           "WHERE d.dateExamen BETWEEN :dateDebut AND :dateFin " +
           "GROUP BY d.joueurId " +
           "ORDER BY nombreBlessures DESC")
    List<Object[]> getStatistiquesParJoueur(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);
    
    @Query("SELECT d.staffMedicalId, COUNT(d), " +
           "AVG(CASE WHEN d.gravite = 'LEGERE' THEN 1 WHEN d.gravite = 'MODEREE' THEN 2 WHEN d.gravite = 'GRAVE' THEN 3 WHEN d.gravite = 'CRITIQUE' THEN 4 ELSE 0 END) as graviteMoyenne " +
           "FROM DonneesSante d " +
           "WHERE d.dateExamen BETWEEN :dateDebut AND :dateFin " +
           "GROUP BY d.staffMedicalId")
    List<Object[]> getStatistiquesParStaffMedical(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);

    // Recherches pour dashboard
    @Query("SELECT COUNT(d) FROM DonneesSante d WHERE d.dateExamen = CURRENT_DATE")
    long countExamensAujourdhui();
    
    @Query("SELECT COUNT(d) FROM DonneesSante d WHERE d.dateExamen >= :dateDebut")
    long countExamensDepuis(@Param("dateDebut") LocalDate dateDebut);
    
    @Query("SELECT d.statut, COUNT(d) FROM DonneesSante d GROUP BY d.statut")
    List<Object[]> countByStatutGlobal();

    // Recherches pour visibilité
    List<DonneesSante> findByJoueurIdAndVisibleJoueurTrueOrderByDateExamenDesc(Long joueurId);
    
    List<DonneesSante> findByJoueurIdAndVisibleJoueurFalseOrderByDateExamenDesc(Long joueurId);

    // Recherches complexes
    @Query("SELECT d FROM DonneesSante d WHERE " +
           "(:joueurId IS NULL OR d.joueurId = :joueurId) AND " +
           "(:staffMedicalId IS NULL OR d.staffMedicalId = :staffMedicalId) AND " +
           "(:typeExamen IS NULL OR d.typeExamen = :typeExamen) AND " +
           "(:statut IS NULL OR d.statut = :statut) AND " +
           "(:gravite IS NULL OR d.gravite = :gravite) AND " +
           "(:dateDebut IS NULL OR d.dateExamen >= :dateDebut) AND " +
           "(:dateFin IS NULL OR d.dateExamen <= :dateFin) " +
           "ORDER BY d.dateExamen DESC")
    Page<DonneesSante> findWithFilters(
            @Param("joueurId") Long joueurId,
            @Param("staffMedicalId") Long staffMedicalId,
            @Param("typeExamen") String typeExamen,
            @Param("statut") String statut,
            @Param("gravite") String gravite,
            @Param("dateDebut") LocalDate dateDebut,
            @Param("dateFin") LocalDate dateFin,
            Pageable pageable);

    // Recherche textuelle
    @Query("SELECT d FROM DonneesSante d WHERE " +
           "LOWER(d.bilan) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(d.diagnostic) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(d.traitement) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(d.blessures) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "ORDER BY d.dateExamen DESC")
    List<DonneesSante> searchByText(@Param("searchTerm") String searchTerm);
}
