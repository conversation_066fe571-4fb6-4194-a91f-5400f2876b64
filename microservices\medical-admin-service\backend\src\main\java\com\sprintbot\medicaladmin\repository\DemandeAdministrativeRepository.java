package com.sprintbot.medicaladmin.repository;

import com.sprintbot.medicaladmin.entity.DemandeAdministrative;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Repository pour la gestion des demandes administratives
 * Fournit les opérations CRUD et requêtes personnalisées
 */
@Repository
public interface DemandeAdministrativeRepository extends JpaRepository<DemandeAdministrative, Long> {

    // Recherches par demandeur
    List<DemandeAdministrative> findByDemandeurIdOrderByDateSoumissionDesc(Long demandeurId);
    
    Page<DemandeAdministrative> findByDemandeurIdOrderByDateSoumissionDesc(Long demandeurId, Pageable pageable);
    
    List<DemandeAdministrative> findByDemandeurIdAndStatutOrderByDateSoumissionDesc(Long demandeurId, String statut);

    // Recherches par type de demandeur
    List<DemandeAdministrative> findByTypeDemandeurOrderByDateSoumissionDesc(String typeDemandeur);
    
    Page<DemandeAdministrative> findByTypeDemandeurOrderByDateSoumissionDesc(String typeDemandeur, Pageable pageable);

    // Recherches par approbateur
    List<DemandeAdministrative> findByApprovateurIdOrderByDateSoumissionDesc(Long approvateurId);
    
    Page<DemandeAdministrative> findByApprovateurIdOrderByDateSoumissionDesc(Long approvateurId, Pageable pageable);
    
    List<DemandeAdministrative> findByApprovateurIdAndStatutOrderByDateSoumissionDesc(Long approvateurId, String statut);

    // Recherches par statut
    List<DemandeAdministrative> findByStatutOrderByDateSoumissionDesc(String statut);
    
    Page<DemandeAdministrative> findByStatutOrderByDateSoumissionDesc(String statut, Pageable pageable);
    
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.statut IN ('EN_ATTENTE', 'EN_TRAITEMENT') ORDER BY d.priorite DESC, d.dateSoumission ASC")
    List<DemandeAdministrative> findDemandesEnCours();
    
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.statut = 'EN_ATTENTE' ORDER BY d.priorite DESC, d.dateSoumission ASC")
    List<DemandeAdministrative> findDemandesEnAttente();

    // Recherches par type de demande
    List<DemandeAdministrative> findByTypeDemande(String typeDemande);
    
    List<DemandeAdministrative> findByTypeDemandeOrderByDateSoumissionDesc(String typeDemande);
    
    Page<DemandeAdministrative> findByTypeDemandeOrderByDateSoumissionDesc(String typeDemande, Pageable pageable);

    // Recherches par priorité
    List<DemandeAdministrative> findByPrioriteOrderByDateSoumissionDesc(String priorite);
    
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.priorite IN ('ELEVEE', 'URGENTE') ORDER BY d.priorite DESC, d.dateSoumission ASC")
    List<DemandeAdministrative> findDemandesUrgentes();
    
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.priorite = 'URGENTE' AND d.statut IN ('EN_ATTENTE', 'EN_TRAITEMENT') ORDER BY d.dateSoumission ASC")
    List<DemandeAdministrative> findDemandesUrgentesEnCours();

    // Recherches par date
    List<DemandeAdministrative> findByDateSoumission(LocalDate dateSoumission);
    
    List<DemandeAdministrative> findByDateSoumissionBetweenOrderByDateSoumissionDesc(LocalDate dateDebut, LocalDate dateFin);
    
    List<DemandeAdministrative> findByDateTraitement(LocalDate dateTraitement);
    
    List<DemandeAdministrative> findByDateTraitementBetweenOrderByDateTraitementDesc(LocalDate dateDebut, LocalDate dateFin);

    // Recherches par échéance
    List<DemandeAdministrative> findByDateEcheance(LocalDate dateEcheance);
    
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.dateEcheance IS NOT NULL AND d.dateEcheance <= CURRENT_DATE AND d.statut IN ('EN_ATTENTE', 'EN_TRAITEMENT') ORDER BY d.dateEcheance ASC")
    List<DemandeAdministrative> findDemandesEchues();
    
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.dateEcheance IS NOT NULL AND d.dateEcheance BETWEEN CURRENT_DATE AND CURRENT_DATE + 7 AND d.statut IN ('EN_ATTENTE', 'EN_TRAITEMENT') ORDER BY d.dateEcheance ASC")
    List<DemandeAdministrative> findDemandesEcheanceProche();

    // Recherches pour validations multiples
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.necessiteValidationCoach = true AND d.validationCoach IS NULL ORDER BY d.dateSoumission ASC")
    List<DemandeAdministrative> findDemandesEnAttenteValidationCoach();
    
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.necessiteValidationMedical = true AND d.validationMedical IS NULL ORDER BY d.dateSoumission ASC")
    List<DemandeAdministrative> findDemandesEnAttenteValidationMedical();
    
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.necessiteValidationFinancier = true AND d.validationFinancier IS NULL ORDER BY d.dateSoumission ASC")
    List<DemandeAdministrative> findDemandesEnAttenteValidationFinancier();

    // Recherches pour impact planning
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.impactPlanning = true AND d.statut IN ('EN_ATTENTE', 'EN_TRAITEMENT') ORDER BY d.dateSoumission ASC")
    List<DemandeAdministrative> findDemandesAvecImpactPlanning();

    // Statistiques
    long countByStatut(String statut);
    
    long countByTypeDemande(String typeDemande);
    
    long countByPriorite(String priorite);
    
    long countByDemandeurId(Long demandeurId);
    
    long countByApprovateurId(Long approvateurId);
    
    long countByTypeDemandeur(String typeDemandeur);

    // Statistiques par période
    @Query("SELECT COUNT(d) FROM DemandeAdministrative d WHERE d.dateSoumission BETWEEN :dateDebut AND :dateFin")
    long countBetweenDates(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);
    
    @Query("SELECT d.statut, COUNT(d) FROM DemandeAdministrative d WHERE d.dateSoumission BETWEEN :dateDebut AND :dateFin GROUP BY d.statut")
    List<Object[]> countByStatutBetweenDates(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);
    
    @Query("SELECT d.typeDemande, COUNT(d) FROM DemandeAdministrative d WHERE d.dateSoumission BETWEEN :dateDebut AND :dateFin GROUP BY d.typeDemande")
    List<Object[]> countByTypeBetweenDates(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);
    
    @Query("SELECT d.priorite, COUNT(d) FROM DemandeAdministrative d WHERE d.dateSoumission BETWEEN :dateDebut AND :dateFin GROUP BY d.priorite")
    List<Object[]> countByPrioriteBetweenDates(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);

    // Recherches pour rapports
    @Query("SELECT d.demandeurId, d.typeDemandeur, COUNT(d), " +
           "SUM(CASE WHEN d.statut = 'VALIDEE' THEN 1 ELSE 0 END) as validees, " +
           "SUM(CASE WHEN d.statut = 'REJETEE' THEN 1 ELSE 0 END) as rejetees, " +
           "AVG(CASE WHEN d.dateTraitement IS NOT NULL THEN d.dateTraitement - d.dateSoumission ELSE NULL END) as delaiMoyenTraitement " +
           "FROM DemandeAdministrative d " +
           "WHERE d.dateSoumission BETWEEN :dateDebut AND :dateFin " +
           "GROUP BY d.demandeurId, d.typeDemandeur " +
           "ORDER BY COUNT(d) DESC")
    List<Object[]> getStatistiquesParDemandeur(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);
    
    @Query("SELECT d.approvateurId, COUNT(d), " +
           "AVG(CASE WHEN d.dateTraitement IS NOT NULL THEN d.dateTraitement - d.dateSoumission ELSE NULL END) as delaiMoyenTraitement, " +
           "SUM(CASE WHEN d.statut = 'VALIDEE' THEN 1 ELSE 0 END) as validees " +
           "FROM DemandeAdministrative d " +
           "WHERE d.dateTraitement BETWEEN :dateDebut AND :dateFin AND d.approvateurId IS NOT NULL " +
           "GROUP BY d.approvateurId")
    List<Object[]> getStatistiquesParApprobateur(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);

    // Recherches pour dashboard
    @Query("SELECT COUNT(d) FROM DemandeAdministrative d WHERE d.dateSoumission = CURRENT_DATE")
    long countDemandesAujourdhui();
    
    @Query("SELECT COUNT(d) FROM DemandeAdministrative d WHERE d.dateSoumission >= CURRENT_DATE - 7")
    long countDemandesSemaineDerniere();
    
    @Query("SELECT COUNT(d) FROM DemandeAdministrative d WHERE d.statut = 'EN_ATTENTE'")
    long countDemandesEnAttente();
    
    @Query("SELECT COUNT(d) FROM DemandeAdministrative d WHERE d.statut = 'EN_TRAITEMENT'")
    long countDemandesEnTraitement();

    // Recherches pour alertes
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.dateSoumission <= CURRENT_DATE - 7 AND d.statut = 'EN_ATTENTE' ORDER BY d.dateSoumission ASC")
    List<DemandeAdministrative> findDemandesAnciennesEnAttente();
    
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.dateSoumission <= CURRENT_DATE - 14 AND d.statut = 'EN_TRAITEMENT' ORDER BY d.dateSoumission ASC")
    List<DemandeAdministrative> findDemandesAnciennesEnTraitement();

    // Recherches par coût
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.coutEstime IS NOT NULL AND d.coutEstime > :montant ORDER BY d.coutEstime DESC")
    List<DemandeAdministrative> findDemandesAvecCoutSuperieur(@Param("montant") Double montant);
    
    @Query("SELECT SUM(d.coutEstime) FROM DemandeAdministrative d WHERE d.statut = 'VALIDEE' AND d.dateSoumission BETWEEN :dateDebut AND :dateFin")
    Double sumCoutEstimeValideesBetweenDates(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);
    
    @Query("SELECT SUM(d.coutReel) FROM DemandeAdministrative d WHERE d.coutReel IS NOT NULL AND d.dateSoumission BETWEEN :dateDebut AND :dateFin")
    Double sumCoutReelBetweenDates(@Param("dateDebut") LocalDate dateDebut, @Param("dateFin") LocalDate dateFin);

    // Recherches complexes avec filtres
    @Query("SELECT d FROM DemandeAdministrative d WHERE " +
           "(:demandeurId IS NULL OR d.demandeurId = :demandeurId) AND " +
           "(:typeDemandeur IS NULL OR d.typeDemandeur = :typeDemandeur) AND " +
           "(:approvateurId IS NULL OR d.approvateurId = :approvateurId) AND " +
           "(:typeDemande IS NULL OR d.typeDemande = :typeDemande) AND " +
           "(:statut IS NULL OR d.statut = :statut) AND " +
           "(:priorite IS NULL OR d.priorite = :priorite) AND " +
           "(:dateDebut IS NULL OR d.dateSoumission >= :dateDebut) AND " +
           "(:dateFin IS NULL OR d.dateSoumission <= :dateFin) " +
           "ORDER BY d.dateSoumission DESC")
    Page<DemandeAdministrative> findWithFilters(
            @Param("demandeurId") Long demandeurId,
            @Param("typeDemandeur") String typeDemandeur,
            @Param("approvateurId") Long approvateurId,
            @Param("typeDemande") String typeDemande,
            @Param("statut") String statut,
            @Param("priorite") String priorite,
            @Param("dateDebut") LocalDate dateDebut,
            @Param("dateFin") LocalDate dateFin,
            Pageable pageable);

    // Recherche textuelle
    @Query("SELECT d FROM DemandeAdministrative d WHERE " +
           "LOWER(d.titre) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(d.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(d.justification) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(d.commentaireApprobateur) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "ORDER BY d.dateSoumission DESC")
    List<DemandeAdministrative> searchByText(@Param("searchTerm") String searchTerm);

    // Recherches pour workflow
    @Query("SELECT d FROM DemandeAdministrative d WHERE d.statut = 'EN_ATTENTE' AND " +
           "((d.necessiteValidationCoach = false OR d.validationCoach = true) AND " +
           "(d.necessiteValidationMedical = false OR d.validationMedical = true) AND " +
           "(d.necessiteValidationFinancier = false OR d.validationFinancier = true)) " +
           "ORDER BY d.priorite DESC, d.dateSoumission ASC")
    List<DemandeAdministrative> findDemandesPretesValidationFinale();
}
